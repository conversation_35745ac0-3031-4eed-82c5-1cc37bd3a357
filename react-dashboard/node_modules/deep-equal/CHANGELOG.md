# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/)
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [v2.2.3](https://github.com/inspect-js/node-deep-equal/compare/v2.2.2...v2.2.3) - 2023-11-08

### Fixed

- [readme] remove performance comment and highlight robustness instead [`#76`](https://github.com/inspect-js/node-deep-equal/issues/76) [`#106`](https://github.com/inspect-js/node-deep-equal/issues/106)

### Commits

- Merge tag 'v1.1.2' [`c90525f`](https://github.com/inspect-js/node-deep-equal/commit/c90525fe8380c8f6680d534eff4f189c33fb1601)
- [Tests] port tests from main; only diff should be true/falses [`e02cadb`](https://github.com/inspect-js/node-deep-equal/commit/e02cadb650ef9d75684c015a35d2c59e029d5174)
- [Dev Deps] update `@ljharb/eslint-config`, `auto-changelog`, `aud`, `eslint`, `set-publish-latest`, `tape` [`11bd45b`](https://github.com/inspect-js/node-deep-equal/commit/11bd45b63943466c13a5ae97276796396a8f2619)
- [Tests] update `.github` from default branch [`58885d3`](https://github.com/inspect-js/node-deep-equal/commit/58885d3280fd25909ca5a17c7cec1e54bdd25233)
- [readme] update readme from default branch [`b0bca9a`](https://github.com/inspect-js/node-deep-equal/commit/b0bca9a1158a9596ec51a0404a6a8272b70ee204)
- [Tests] add `nyc` for coverage [`e25bc37`](https://github.com/inspect-js/node-deep-equal/commit/e25bc3716c7ec0390b06bd7b6fac0c9ab0968ab4)
- [readme] update badge URLs, fix line breaking [`1d58c6e`](https://github.com/inspect-js/node-deep-equal/commit/1d58c6ecbab5275e17735d352ac7fa4a1af40aca)
- [Tests] use `Buffer.from` when available [`f0d4a42`](https://github.com/inspect-js/node-deep-equal/commit/f0d4a42fb87854e17d78cb2d6e9ec0156ea3d2f3)
- [Tests] use `has-proto` [`0263fb9`](https://github.com/inspect-js/node-deep-equal/commit/0263fb9170a4e6a400c3b628cfba902ebaf59df5)
- [Deps] update `is-arguments`, `is-date-object`, `is-regex`, `object-is`, `regexp.prototype.flags` [`80c15ca`](https://github.com/inspect-js/node-deep-equal/commit/80c15cae82164c888de8635287c3b44ab84b3fd4)
- [meta] add missing `engines.node` [`e1d08a8`](https://github.com/inspect-js/node-deep-equal/commit/e1d08a818fdb69e4f39b7aa23dc88092d3339c43)
- [meta] use `npmignore` to autogenerate an npmignore file [`e0770e5`](https://github.com/inspect-js/node-deep-equal/commit/e0770e594ec683ded32fd1b14e876f087fc04f4a)
- [Deps] update `is-date-object`, `is-regex`, `object-is`, `regexp.prototype.flags` [`e4fb8c6`](https://github.com/inspect-js/node-deep-equal/commit/e4fb8c6459aa13d3a110b45e5612c31890a5d94e)
- [Tests] handle ported test failures in iojs v2 [`3798ff4`](https://github.com/inspect-js/node-deep-equal/commit/3798ff490286ee9ba70cf50c339914be355eea18)
- [Deps] update `call-bind`, `regexp.prototype.flags`, `which-typed-array` [`540e3a1`](https://github.com/inspect-js/node-deep-equal/commit/540e3a119dcd94b30253ad380fde69d5531cf0ac)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `tape` [`0f8ca75`](https://github.com/inspect-js/node-deep-equal/commit/0f8ca7575e44c66fdf1dc2b6ad4e69e1b549369f)
- [Tests] handle some additional test differences in node &lt;= 0.10 [`197a220`](https://github.com/inspect-js/node-deep-equal/commit/197a2203f0a95828c62ff2b3c980ddf16736913c)
- [Dev Deps] update `object.getownpropertydescriptors`, `tape` [`21851a6`](https://github.com/inspect-js/node-deep-equal/commit/21851a62cda0b832800de3070d9fbb7216e43284)
- [Dev Deps] update `semver`, `tape` [`dd440b2`](https://github.com/inspect-js/node-deep-equal/commit/dd440b2267ac8fa496b907369d3e0b73f9439e87)
- [meta] add missing `engines.node` [`e158993`](https://github.com/inspect-js/node-deep-equal/commit/e158993fcf913aca8a1ffdc037abfeeb3a5cb4e5)
- [meta] update `.gitignore` from default branch [`6ee186b`](https://github.com/inspect-js/node-deep-equal/commit/6ee186bd39199a1c05421e200b1e8e31a83cf363)
- [Deps] update `get-intrinsic` [`6da4b86`](https://github.com/inspect-js/node-deep-equal/commit/6da4b86e4d7ad3c01736006ca129b8e3c3f2c82c)
- [Dev Deps] update `tape` [`6ada1ab`](https://github.com/inspect-js/node-deep-equal/commit/6ada1ab7f9a176b10f0117a8e10b785965c0e830)
- [Dev Deps] update `tape` [`270d34b`](https://github.com/inspect-js/node-deep-equal/commit/270d34b48401ee40792bf18d06614044246776dd)
- [meta] fix URLs [`a269c18`](https://github.com/inspect-js/node-deep-equal/commit/a269c183bccb78dfdd2c88a149fe5373f13af4c0)
- [readme] update default branch name [`030a63f`](https://github.com/inspect-js/node-deep-equal/commit/030a63f40afa3d468ffb10437cbf5acaa677fea2)
- [Deps] update `which-typed-array` [`2f0c327`](https://github.com/inspect-js/node-deep-equal/commit/2f0c327eaaed7b50649c367f88944b6dd7e9f18c)
- [Tests] only use `Buffer.from` when it has a length of &gt; 1 [`f7e5776`](https://github.com/inspect-js/node-deep-equal/commit/f7e577622d943da78c2c9cd1e070eb786cf7a68b)

## [v2.2.2](https://github.com/inspect-js/node-deep-equal/compare/v2.2.1...v2.2.2) - 2023-07-10

### Commits

- [Fix] avoid an infinite loop in node 0.8 with Typed Arrays [`f247282`](https://github.com/inspect-js/node-deep-equal/commit/f247282e66060087c51221f77b9170fa0e2e461b)
- [Dev Deps] update `@ljharb/eslint-config`, `aud`, `tape` [`fa25583`](https://github.com/inspect-js/node-deep-equal/commit/fa255839c1afed228c41ca76177a8c239ec9305a)
- [Deps] update `get-intrinsic` [`3638470`](https://github.com/inspect-js/node-deep-equal/commit/3638470e71ed6185e06a0f0694e77cbf4c2b791c)

## [v2.2.1](https://github.com/inspect-js/node-deep-equal/compare/v2.2.0...v2.2.1) - 2023-04-27

### Commits

- [Tests] add passing tests covering object key ordering [`51c9ff6`](https://github.com/inspect-js/node-deep-equal/commit/51c9ff6b4c5d4371e0f444ce1160e416bf88e21f)
- [Refactor] use `array-buffer-byte-length` [`f47822c`](https://github.com/inspect-js/node-deep-equal/commit/f47822cf0c188caa71dfbb107b081324fcb6b99d)
- [Deps] update `is-array-buffer`, `regexp.prototype.flags` [`160d851`](https://github.com/inspect-js/node-deep-equal/commit/160d8513335d63256f9826835b35d1a462c76771)
- [Deps] update `es-get-iterator`, `get-intrinsic` [`2e08e95`](https://github.com/inspect-js/node-deep-equal/commit/2e08e956a43c839246d2280cfaaa3f3200937bbd)
- [Dev Deps] update `object.getownpropertydescriptors` [`63e9ab7`](https://github.com/inspect-js/node-deep-equal/commit/63e9ab7450212bf7e393c1bf725293389cb4626a)
- [Dev Deps] update `tape` [`c8db964`](https://github.com/inspect-js/node-deep-equal/commit/c8db964f605834388b0df20c5fce49cfbaa541bc)

## [v2.2.0](https://github.com/inspect-js/node-deep-equal/compare/v2.1.0...v2.2.0) - 2023-01-05

### Commits

- [New] add support for `SharedArrayBuffer` [`0ef51c7`](https://github.com/inspect-js/node-deep-equal/commit/0ef51c79b7dcd6ae5890b2dce4304be7c8ebe314)
- [Refactor] use `is-array-buffer` package [`0360ee1`](https://github.com/inspect-js/node-deep-equal/commit/0360ee1f1bb6e5804c15a9a4aa75ff84120a33e1)
- [Fix] `whichTypedArray` can return false in both cases [`df3e3c4`](https://github.com/inspect-js/node-deep-equal/commit/df3e3c4e30552b7a7892b62dc45fccd31eac9bfe)
- [Dev Deps] update `aud`, `object.getownpropertydescriptors` [`6d62ea4`](https://github.com/inspect-js/node-deep-equal/commit/6d62ea4f2b82d4f861fd3e0c2d67916939b1edbc)
- [Dev Deps] update `@ljharb/eslint-config` [`15aab59`](https://github.com/inspect-js/node-deep-equal/commit/15aab59d647628e05b77d1897036d73884f7a107)
- [Deps] update `which-typed-array` [`8dfce27`](https://github.com/inspect-js/node-deep-equal/commit/8dfce27f333721c697b90569b26e4bded0d61d88)
- [eslint] remove `.eslintignore` [`af46773`](https://github.com/inspect-js/node-deep-equal/commit/af46773b281dc67d7743280505a22248f0f0748d)

## [v2.1.0](https://github.com/inspect-js/node-deep-equal/compare/v2.0.5...v2.1.0) - 2022-11-02

### Fixed

- [New] add support for `ArrayBuffer` [`#94`](https://github.com/inspect-js/node-deep-equal/issues/94)

### Commits

- [actions] reuse common workflows [`12de287`](https://github.com/inspect-js/node-deep-equal/commit/12de2873def84c621421b1e3d4a96babf2ceb30a)
- [actions] use `node/install` instead of `node/run`; use `codecov` action [`ec248b9`](https://github.com/inspect-js/node-deep-equal/commit/ec248b96f4f91eb4c5971dfec74d6d91f04b39cd)
- [Tests] run assert tests on a dynamic matrix [`695aebd`](https://github.com/inspect-js/node-deep-equal/commit/695aebd50945621984bfa7e60b5c99f79dbacf1a)
- [readme] add badges [`492e159`](https://github.com/inspect-js/node-deep-equal/commit/492e1591585233184151b5526cf5413e61a2aa89)
- [Test] ArrayBuffers in node &lt; 0.12 have a nonconfigurable own `byteLength` property [`40f4b87`](https://github.com/inspect-js/node-deep-equal/commit/40f4b8719658f6262399b24791d4ccb5960ca8ea)
- [meta] use `npmignore` to autogenerate an npmignore file [`2627269`](https://github.com/inspect-js/node-deep-equal/commit/26272693147d68ec3ae2216b1f3ac5d30b79b3da)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `safe-publish-latest`, `tape` [`7a05bb7`](https://github.com/inspect-js/node-deep-equal/commit/7a05bb7482817616243fc89956abf48710d77fea)
- [Deps] update `call-bind`, `es-get-iterator`, `get-intrinsic`, `is-arguments`, `is-regex`, `regexp.prototype.flags`, `side-channel`, `which-boxed-primitive`, `which-typed-array` [`c0844e6`](https://github.com/inspect-js/node-deep-equal/commit/c0844e629ba3dc5193fd41a75ebcedaba976c1af)
- [actions] update codecov uploader [`b1b0552`](https://github.com/inspect-js/node-deep-equal/commit/b1b0552fe3dc23029a2717f84642e53548902e68)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `auto-changelog`, `has-symbols`, `object.getownpropertydescriptors`, `tape` [`9a1ccf5`](https://github.com/inspect-js/node-deep-equal/commit/9a1ccf5285317962fe37baa211c2b20081be71ef)
- [meta] do not publish github action workflow files [`2d63384`](https://github.com/inspect-js/node-deep-equal/commit/2d63384d6676b593c2a74b45747ac81cea3da76e)
- [actions] update rebase action to use reusable workflow [`69d21f5`](https://github.com/inspect-js/node-deep-equal/commit/69d21f55af0bcff1c41447a6ac396ae556fda226)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `has-symbols`, `object.getownpropertydescriptors`, `tape` [`297aae8`](https://github.com/inspect-js/node-deep-equal/commit/297aae88f619b1e058418be32e37fc29770b4f21)
- [Tests] add tests for regexes with the same source but different flags [`29c8a0d`](https://github.com/inspect-js/node-deep-equal/commit/29c8a0da09567b3bbe6614c1e235d5a87d7bdaf2)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config` [`9e64ec9`](https://github.com/inspect-js/node-deep-equal/commit/9e64ec93a8c5cbe87315d30dd62a8fe6dcf842b4)
- [Deps] update `is-arguments`, `is-date-object`, `is-regex`, `which-typed-array` [`edfb752`](https://github.com/inspect-js/node-deep-equal/commit/edfb752810699790c06bb424174f38b449a4b8fb)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `auto-changelog`, `tape` [`7b5f7a3`](https://github.com/inspect-js/node-deep-equal/commit/7b5f7a348b2bb78ca1c00003d0dd3444d5a372a6)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `has-typed-arrays`, `tape` [`e328a23`](https://github.com/inspect-js/node-deep-equal/commit/e328a237ba9c262d8c2d178a65d5e1f744cbbd6b)
- [Deps] update `get-intrinsic`, `regexp.prototype.flags`, `which-typed-array` [`29123a7`](https://github.com/inspect-js/node-deep-equal/commit/29123a71857c27cce497ac991fa9be0e5921adce)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud` [`b7039ef`](https://github.com/inspect-js/node-deep-equal/commit/b7039effc1b751cb579095f9738ee019d8458a41)
- [Dev Deps] update `aud`, `tape` [`9d0b2e0`](https://github.com/inspect-js/node-deep-equal/commit/9d0b2e07df24dfd1d4d9bf32cdca07e6e1377f23)
- [actions] update workflows [`a5f5a7e`](https://github.com/inspect-js/node-deep-equal/commit/a5f5a7ed570e35cefad08ed32652cb8647d0166c)
- [Deps] update `get-intrinsic`, `object.assign` [`02025e2`](https://github.com/inspect-js/node-deep-equal/commit/02025e21dfbce6a4d52421c4892ba743dd7a7540)
- [Test] node &lt; 4 lacks `ArrayBuffer.prototype.fill` [`bb65b07`](https://github.com/inspect-js/node-deep-equal/commit/bb65b07d39eeea03ca066a92892a06a607641ed7)
- [Deps] update `is-date-object`, `is-regex` [`a463ed2`](https://github.com/inspect-js/node-deep-equal/commit/a463ed26ba7ca4b818db4929e4e946ddc0590bce)
- [Deps] update `get-intrinsic`, `object-is` [`2a33cce`](https://github.com/inspect-js/node-deep-equal/commit/2a33cced17827df191676ecdd9b9bc4f0d814aa6)
- [actions] update workflows [`d905278`](https://github.com/inspect-js/node-deep-equal/commit/d90527855a35bcaeef70d3e21370580d7d182c8d)
- [meta] use `prepublishOnly` script for npm 7+ [`7b66184`](https://github.com/inspect-js/node-deep-equal/commit/7b661846aedb0ad5768f1118e93c662d214c85f8)
- [Dev Deps] update `object.getownpropertydescriptors` [`d970dbc`](https://github.com/inspect-js/node-deep-equal/commit/d970dbcf4a95c58d5bbd6ea507455bbfa1e7ec02)

## [v2.0.5](https://github.com/inspect-js/node-deep-equal/compare/v2.0.4...v2.0.5) - 2020-12-01

### Commits

- [Tests] migrate tests to Github Actions [`4051a18`](https://github.com/inspect-js/node-deep-equal/commit/4051a1811612fd1b107743a092402361f597f89a)
- [meta] add Automatic Rebase and Require Allow Edits workflows [`6682b15`](https://github.com/inspect-js/node-deep-equal/commit/6682b15417630d6c57c854d088dfcd6023b3e185)
- [Deps] use `call-bind` and `get-intrinsic` instead of `es-abstract`; update `es-get-iterator`, `object-is`, `object.assign` [`a1d6d93`](https://github.com/inspect-js/node-deep-equal/commit/a1d6d931ebc80db8a6dba1bd453165356458f9e1)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `object.getownpropertydescriptors` [`b142f4b`](https://github.com/inspect-js/node-deep-equal/commit/b142f4b9458c320b944dc06f57b9339530cf404e)

## [v2.0.4](https://github.com/inspect-js/node-deep-equal/compare/v2.0.3...v2.0.4) - 2020-10-01

### Fixed

- [meta] fix incorrect `./package.json` exports entry [`#89`](https://github.com/inspect-js/node-deep-equal/issues/89)

### Commits

- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `auto-changelog` [`d6bfee3`](https://github.com/inspect-js/node-deep-equal/commit/d6bfee329e2a430a12e6a4aba7006c5d7c36020e)
- [Tests] account for node 14+ making two NaNs loosely deep equal [`9367954`](https://github.com/inspect-js/node-deep-equal/commit/93679546d97dbd92d615b0f758d733ea1fb4eb4b)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `aud`, `tape` [`86a159b`](https://github.com/inspect-js/node-deep-equal/commit/86a159b72c3e3620285498113251220d3c5d05f0)
- [Deps] update `es-abstract`, `object-is`, `object.assign` [`e87979d`](https://github.com/inspect-js/node-deep-equal/commit/e87979d311faa537622a6c9faee6e91cf9a08bcd)
- [Deps] update `es-abstract`, `is-regex` [`d0cacca`](https://github.com/inspect-js/node-deep-equal/commit/d0cacca140b963d8abbe10f6bf640f22ef11f7b0)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `tape` [`375a9e1`](https://github.com/inspect-js/node-deep-equal/commit/375a9e1e4911b3210e328365432c8cf946d0a7e2)
- [Dev Deps] update `eslint`, `auto-changelog` [`e562d4f`](https://github.com/inspect-js/node-deep-equal/commit/e562d4fadebf09b1fca02d6f43e1c933c6ef8242)
- [Deps] update `side-channel` [`f092342`](https://github.com/inspect-js/node-deep-equal/commit/f092342373d4a33b3f679042f357fd800185caad)

## [v2.0.3](https://github.com/inspect-js/node-deep-equal/compare/v2.0.2...v2.0.3) - 2020-04-23

### Commits

- [Deps] update `object-is`, `which-typed-array` [`c232374`](https://github.com/inspect-js/node-deep-equal/commit/c2323741001f7cb2414f4fa26301f1b9705e6b46)
- [Dev Deps] update `auto-changelog`; add `aud` [`c80a91d`](https://github.com/inspect-js/node-deep-equal/commit/c80a91d8b9815300fc9e24f08094cac8942d9da7)
- [Dev Deps] update `auto-changelog` [`295d195`](https://github.com/inspect-js/node-deep-equal/commit/295d19529365a87ed663eecf26f19effe5b06f3c)
- [Dev Deps] update `aud` [`1134cc2`](https://github.com/inspect-js/node-deep-equal/commit/1134cc26fd112e3679a6973f4d418c4495e3358d)
- [Deps] Fix classification of `object.assign` [`32695dd`](https://github.com/inspect-js/node-deep-equal/commit/32695dd88c3eb86c87e162830e0e1c55447a4b9f)

## [v2.0.2](https://github.com/inspect-js/node-deep-equal/compare/v2.0.1...v2.0.2) - 2020-04-01

### Commits

- [Fix] properly pass `strict` option through internals [`4e2919d`](https://github.com/inspect-js/node-deep-equal/commit/4e2919d7c403413a9cf094b2f3be095653670e97)
- [Tests] add more coverage [`a3d8677`](https://github.com/inspect-js/node-deep-equal/commit/a3d86774fa39e1a00b3371a32fbe42c7b38743ff)
- [Tests] add coverage reporting [`3174e33`](https://github.com/inspect-js/node-deep-equal/commit/3174e33f952a4bdd57c43e3b40dea705d3d61e68)
- [Refactor] add strict pragma; remove unused try/catch [`c1d173e`](https://github.com/inspect-js/node-deep-equal/commit/c1d173ebadfcc4a2574f28b17727239dcd2737be)
- [Fix] the `prototype` property is not special on non-functions [`1c3cc25`](https://github.com/inspect-js/node-deep-equal/commit/1c3cc2526766e52cc32b9c1859b7073d5dbb70aa)
- [Deps] update `es-abstract`, `es-get-iterator`, `is-date-object`, `is-regex`, `object-is`, `regexp.prototype.flags`, `side-channel` [`7787bdc`](https://github.com/inspect-js/node-deep-equal/commit/7787bdc1268620ef12d17777fce3682abc4e364e)
- [Deps] update `es-abstract`, `es-get-iterator`, `which-collection` [`87624cf`](https://github.com/inspect-js/node-deep-equal/commit/87624cf96f2ca00bf5200aa860c4574dba96f4aa)
- [Fix] robustly distinguish Typed Arrays [`7ccb2b0`](https://github.com/inspect-js/node-deep-equal/commit/7ccb2b0f88136516cd58cad74d9c0a20daad824c)
- [Dev Deps] update `@ljharb/eslint-config`, `tape` [`c1814a1`](https://github.com/inspect-js/node-deep-equal/commit/c1814a1256e373ac6e7953b354f9f56ff5b7fe89)
- [Dev Deps] update `@ljharb/eslint-config`, `object.getownpropertydescriptors`, `tape` [`7a0d819`](https://github.com/inspect-js/node-deep-equal/commit/7a0d819e5b3807eab873d7961eb34e30ff615943)
- [Dev Deps] update `auto-changelog`, `tape` [`f248756`](https://github.com/inspect-js/node-deep-equal/commit/f2487567b092d90005a7d09b084f7ffb9ba71257)
- [Tests] fix `tag` helper, from bb50ea2e6d7906cd58683fdc7d327660e71612f4 [`469f008`](https://github.com/inspect-js/node-deep-equal/commit/469f0089d16d8678ee4c5f57016d40f072b3f7be)
- [Dev Deps] update `eslint`, `tape` [`6537dce`](https://github.com/inspect-js/node-deep-equal/commit/6537dcef16c792546c3a40ea5b53ab43861cf719)
- [Fix] make `isBuffer` check slightly more robust [`c21e14d`](https://github.com/inspect-js/node-deep-equal/commit/c21e14d7afe7d53391fa95fd8c59f85affc02ac6)
- [meta] disable greenkeeper updates for semver [`724f051`](https://github.com/inspect-js/node-deep-equal/commit/724f051213e159d5a021d0649e6a2cafd4c13458)
- [Deps] update `es-abstract` [`942ad86`](https://github.com/inspect-js/node-deep-equal/commit/942ad8699442dd83207bb04ae079997b3433ca60)
- [Deps] update `es-abstract` [`8724052`](https://github.com/inspect-js/node-deep-equal/commit/8724052e10d2e897626af6f46e0bce9d35523b04)

## [v2.0.1](https://github.com/inspect-js/node-deep-equal/compare/v2.0.0...v2.0.1) - 2019-12-08

### Fixed

- [Fix] `Map` and `Set` comparison: keys are loosely compared also [`#75`](https://github.com/inspect-js/node-deep-equal/issues/75)

### Commits

- [Fix] check extra properties on regexps [`1eac46b`](https://github.com/inspect-js/node-deep-equal/commit/1eac46b69c7b499cecb7e3b555b6e260de2043fd)
- [Tests] update test skipping logic [`8adf08d`](https://github.com/inspect-js/node-deep-equal/commit/8adf08dcb91146590f2c57b614de6a853054f297)

## [v2.0.0](https://github.com/inspect-js/node-deep-equal/compare/v1.1.2...v2.0.0) - 2019-12-03

### Fixed

- [Fix] handle circular references [`#19`](https://github.com/inspect-js/node-deep-equal/issues/19)
- [Breaking] compare `Map`, `Set`, `WeakMap`, `WeakSet` matching node assert [`#54`](https://github.com/inspect-js/node-deep-equal/issues/54) [`#46`](https://github.com/inspect-js/node-deep-equal/issues/46)
- [Breaking] compare `Map`, `Set`, `WeakMap`, `WeakSet` matching node assert [`#54`](https://github.com/inspect-js/node-deep-equal/issues/54) [`#46`](https://github.com/inspect-js/node-deep-equal/issues/46)
- [Breaking] comparing arrays and objects should always fail, per node‘s assert.deepEqual [`#12`](https://github.com/inspect-js/node-deep-equal/issues/12)

### Commits

- [Tests] add passing tests from node core [`91a6472`](https://github.com/inspect-js/node-deep-equal/commit/91a6472719331eca1b13472db798e7f3188af9f7)
- [Tests] convert remaining tests to use the custom assertion [`2bb7269`](https://github.com/inspect-js/node-deep-equal/commit/2bb7269013574a9a4e84ec5e34a04258f3ecd94a)
- [Tests] add tests for getters, fake arrays, overriding getTime on Dates [`1b66d90`](https://github.com/inspect-js/node-deep-equal/commit/1b66d90232a5a576d0334f0c109f8c79da487c9f)
- [Breaking] non-Date objects with different [[Prototypes]] are not equal [`5f2f2e5`](https://github.com/inspect-js/node-deep-equal/commit/5f2f2e55a3cfea251ca502a7846489a9133b4347)
- [Breaking] boxed primitives are not silently unboxed [`e207bc3`](https://github.com/inspect-js/node-deep-equal/commit/e207bc352f338a5c57b2ef2ce59d78b821fb2b58)
- [Tests] run tests with assert, as well [`ccaf3ea`](https://github.com/inspect-js/node-deep-equal/commit/ccaf3eabb081d255f9fafcf2cc2631eafaa9b2a7)
- [Breaking] boxed primitives are unwrapped for comparisons [`a159a3a`](https://github.com/inspect-js/node-deep-equal/commit/a159a3a0660a2da9a8ff6392aba4b2de81bc8391)
- [Refactor] use `side-channel` for memoizing circular refs [`983780a`](https://github.com/inspect-js/node-deep-equal/commit/983780aec338b8d909c3f6a5b1e0f4d7fe1d78b5)
- [Tests] Failing tests for Map equality [`4856185`](https://github.com/inspect-js/node-deep-equal/commit/4856185d989914e2f440fe3fd3bac4d977365fe2)
- [Tests] add `toStringTag` helper [`bb50ea2`](https://github.com/inspect-js/node-deep-equal/commit/bb50ea2e6d7906cd58683fdc7d327660e71612f4)
- [Fix] robustly unbox boxed primitives, using `unbox-primitive` [`c455998`](https://github.com/inspect-js/node-deep-equal/commit/c45599893f02f349604a7b39b39f45036ea2e499)
- [Breaking] two objects with different `Symbol.toStringTag`s are not equal [`45431b6`](https://github.com/inspect-js/node-deep-equal/commit/45431b67b5e7e9cb91d5ff8706dc55348d59184e)
- [Tests] add more Error tests [`c0ae38c`](https://github.com/inspect-js/node-deep-equal/commit/c0ae38c43872621da83aba608cd62b951de7c4c6)
- [Breaking] ensure `Error` objects compare properly [`ffb2522`](https://github.com/inspect-js/node-deep-equal/commit/ffb2522eb94afe46a8ccc23faebdcee23ea6f170)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config` [`737031e`](https://github.com/inspect-js/node-deep-equal/commit/737031e9ac09812ec7181040e857d8b73b573970)
- [readme] update URLs, readme [`9d300da`](https://github.com/inspect-js/node-deep-equal/commit/9d300da78ce21dcca7259bb557765cfac0db3e16)
- [Tests] use `Buffer.from` when available [`43fa5da`](https://github.com/inspect-js/node-deep-equal/commit/43fa5dafe17b1287f56c2566c487a9c05946fe4f)
- [Breaking] add `exports` to `package.json` [`ecd15ae`](https://github.com/inspect-js/node-deep-equal/commit/ecd15aedeeddc8f386ed8b54296648ee4a73af9b)
- [Tests] require ASSERT tests to pass [`7ac77cf`](https://github.com/inspect-js/node-deep-equal/commit/7ac77cf21081b30eeadb5ac5201b30ad0d4dcbe4)
- [Robustness] use `callBound` from `es-abstract` [`4dc3477`](https://github.com/inspect-js/node-deep-equal/commit/4dc347792be1f7131b3095ba98590a599cce7e20)
- [patch] [[Prototype]]s are only compared in strict mode [`43b1eef`](https://github.com/inspect-js/node-deep-equal/commit/43b1eef2eb7bc3045fb160bf5d755bce44cbfd52)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `has-symbols` [`89228df`](https://github.com/inspect-js/node-deep-equal/commit/89228df447b174008e163f1c83c8f0f559e30fa0)
- [Tests] up to `node` `v12.9` [`595c81a`](https://github.com/inspect-js/node-deep-equal/commit/595c81a2e2c1baab216b2dac640ed020f49033bf)
- [Fix] pass options when comparing Collection keys/values [`9859584`](https://github.com/inspect-js/node-deep-equal/commit/9859584ae2f4f46ec602e18bac9610c71ff00e2f)
- [Breaking] `false` and `[]` are no longer equal, even non-strictly [`5c3accc`](https://github.com/inspect-js/node-deep-equal/commit/5c3acccf541ba0b77342c2a441b32a43acebc73e)
- [Tests] only use `Buffer.from` when it has a length of &gt; 1 [`3645129`](https://github.com/inspect-js/node-deep-equal/commit/3645129d7692c5195ae4b94ba83d2fe84d9538d7)
- [Breaking] remove `lib/is-arguments` and `lib/keys` entrypoints [`783d47e`](https://github.com/inspect-js/node-deep-equal/commit/783d47e3092b7a62aaecbf6836b18a0dfca96662)
- [Dev Deps] update `eslint` [`7cc1dd9`](https://github.com/inspect-js/node-deep-equal/commit/7cc1dd986fc30946ca06ba6496ef856cfb911cf3)
- Merge tag 'v1.1.1' [`f9d3c6d`](https://github.com/inspect-js/node-deep-equal/commit/f9d3c6d1e4a895c448ba7d8766f8616d0e235c9a)
- Merge tag 'v1.1.0' [`0aeca99`](https://github.com/inspect-js/node-deep-equal/commit/0aeca9988dba6fe3318f46c47c933e6926175278)

## [v1.1.2](https://github.com/inspect-js/node-deep-equal/compare/v1.1.1...v1.1.2) - 2023-11-08

### Commits

- [Tests] port tests from main; only diff should be true/falses [`e02cadb`](https://github.com/inspect-js/node-deep-equal/commit/e02cadb650ef9d75684c015a35d2c59e029d5174)
- [Dev Deps] update `@ljharb/eslint-config`, `auto-changelog`, `aud`, `eslint`, `set-publish-latest`, `tape` [`11bd45b`](https://github.com/inspect-js/node-deep-equal/commit/11bd45b63943466c13a5ae97276796396a8f2619)
- [Tests] update `.github` from default branch [`58885d3`](https://github.com/inspect-js/node-deep-equal/commit/58885d3280fd25909ca5a17c7cec1e54bdd25233)
- [readme] update readme from default branch [`b0bca9a`](https://github.com/inspect-js/node-deep-equal/commit/b0bca9a1158a9596ec51a0404a6a8272b70ee204)
- [Tests] add `nyc` for coverage [`e25bc37`](https://github.com/inspect-js/node-deep-equal/commit/e25bc3716c7ec0390b06bd7b6fac0c9ab0968ab4)
- [Tests] use `Buffer.from` when available [`f0d4a42`](https://github.com/inspect-js/node-deep-equal/commit/f0d4a42fb87854e17d78cb2d6e9ec0156ea3d2f3)
- [Deps] update `is-arguments`, `is-date-object`, `is-regex`, `object-is`, `regexp.prototype.flags` [`80c15ca`](https://github.com/inspect-js/node-deep-equal/commit/80c15cae82164c888de8635287c3b44ab84b3fd4)
- [meta] use `npmignore` to autogenerate an npmignore file [`e0770e5`](https://github.com/inspect-js/node-deep-equal/commit/e0770e594ec683ded32fd1b14e876f087fc04f4a)
- [Deps] update `is-date-object`, `is-regex`, `object-is`, `regexp.prototype.flags` [`e4fb8c6`](https://github.com/inspect-js/node-deep-equal/commit/e4fb8c6459aa13d3a110b45e5612c31890a5d94e)
- [Tests] handle ported test failures in iojs v2 [`3798ff4`](https://github.com/inspect-js/node-deep-equal/commit/3798ff490286ee9ba70cf50c339914be355eea18)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`, `tape` [`0f8ca75`](https://github.com/inspect-js/node-deep-equal/commit/0f8ca7575e44c66fdf1dc2b6ad4e69e1b549369f)
- [Tests] handle some additional test differences in node &lt;= 0.10 [`197a220`](https://github.com/inspect-js/node-deep-equal/commit/197a2203f0a95828c62ff2b3c980ddf16736913c)
- [meta] add missing `engines.node` [`e158993`](https://github.com/inspect-js/node-deep-equal/commit/e158993fcf913aca8a1ffdc037abfeeb3a5cb4e5)
- [meta] update `.gitignore` from default branch [`6ee186b`](https://github.com/inspect-js/node-deep-equal/commit/6ee186bd39199a1c05421e200b1e8e31a83cf363)
- [Dev Deps] update `tape` [`270d34b`](https://github.com/inspect-js/node-deep-equal/commit/270d34b48401ee40792bf18d06614044246776dd)
- [meta] fix URLs [`a269c18`](https://github.com/inspect-js/node-deep-equal/commit/a269c183bccb78dfdd2c88a149fe5373f13af4c0)
- [Tests] only use `Buffer.from` when it has a length of &gt; 1 [`f7e5776`](https://github.com/inspect-js/node-deep-equal/commit/f7e577622d943da78c2c9cd1e070eb786cf7a68b)

## [v1.1.1](https://github.com/inspect-js/node-deep-equal/compare/v1.1.0...v1.1.1) - 2019-11-12

### Commits

- [Tests] use shared travis-ci configs [`678cb2a`](https://github.com/inspect-js/node-deep-equal/commit/678cb2a34ef920eab563af69bdf402b9273c3e2c)
- [meta] add `auto-changelog` [`569bcce`](https://github.com/inspect-js/node-deep-equal/commit/569bcce030784f7506367055122c54c6cac00ebc)
- [Dev Deps] update `eslint`, `@ljharb/eslint-config`; add `safe-publish-latest` [`9dedd40`](https://github.com/inspect-js/node-deep-equal/commit/9dedd409cb74c538153287db546a14f2fa583e6f)
- [Tests] add `npx aud` in `posttest` [`0b76639`](https://github.com/inspect-js/node-deep-equal/commit/0b766394504f74d002ad85e7b8244ce89bb27130)
- [meta] add `funding` field [`3e519e3`](https://github.com/inspect-js/node-deep-equal/commit/3e519e36998ea82120cfe9a509dc0090a87d7063)

## [v1.1.0](https://github.com/inspect-js/node-deep-equal/compare/v1.0.1...v1.1.0) - 2019-08-27

### Commits

- [Tests] up to `node` `v12.7`, `v11.15`, `v10.16`, `v9.11`, `v8.16`, `v7.10`, `v6.17`, `4.9`; use `nvm install-latest-npm` [`ba11166`](https://github.com/inspect-js/node-deep-equal/commit/ba111664928f3a9279528dd05330b974950003b4)
- [Tests] make a `tape` helper, to easily compare loose/strict, and (a,b)/(b,a) arg order [`0c25e94`](https://github.com/inspect-js/node-deep-equal/commit/0c25e9438b2d4a96e75c3c2e6fbb353ae450ed1f)
- [Tests] add `npm run lint` [`8590816`](https://github.com/inspect-js/node-deep-equal/commit/8590816be6bc57a07fbc160985f5f04f11c7fa97)
- test: Fails simple array tests on IE &lt; 9 #10 [`bc37498`](https://github.com/inspect-js/node-deep-equal/commit/bc374988ee763a881ef39d8f6949838ec4df4e64)
- [Tests] clean up/flesh out test cases [`3fffed9`](https://github.com/inspect-js/node-deep-equal/commit/3fffed9a7e095bbf922561fa0b18681422861af8)
- [Fix] (array,arguments) now behaves the same as (arguments,array) [`a9528ff`](https://github.com/inspect-js/node-deep-equal/commit/a9528ff71561c303e722eb5ae0a3b9b2ecae4a3b)
- [Refactor] move Date and RegExp tests into `objEquiv` [`5b3bf31`](https://github.com/inspect-js/node-deep-equal/commit/5b3bf315b603f00bab49e78c05c075cdc0dfd506)
- [Fix] properly compare RegExp objects [`b8c179c`](https://github.com/inspect-js/node-deep-equal/commit/b8c179c5aa91c8a2f71f053e2d9e2d477780250e)
- [Refactor] Use `object-keys` and `is-arguments` instead of a homegrown shim. [`3b503fb`](https://github.com/inspect-js/node-deep-equal/commit/3b503fb262c431df72a6c91cbb384be557315645)
- [Fix] use `object-is` to support `NaN` and `-0` properly [`28fde4a`](https://github.com/inspect-js/node-deep-equal/commit/28fde4a411073e3131d3637248f8c25aac9329f2)
- [meta] fix license text so GitHub can detect it [`7000403`](https://github.com/inspect-js/node-deep-equal/commit/700040347119ce46b3e917e800d87628af1384f9)
- [Tests] add array/object tests [`558eecf`](https://github.com/inspect-js/node-deep-equal/commit/558eecf9daa815468f0bc4c9799041507b7ffa15)
- [Fix] ensure Buffer + non-Buffer comparison order does not matter [`de4e75c`](https://github.com/inspect-js/node-deep-equal/commit/de4e75ce7f0621cdc09ae8cbdc82de9e4c17578e)
- [Refactor] `objEquiv`: bail early if typeofs are different [`410e2d6`](https://github.com/inspect-js/node-deep-equal/commit/410e2d63730763e7e92c9b45b916f97058ab31f7)
- [Tests] add tests for `Object.create(null)` [`ed266e8`](https://github.com/inspect-js/node-deep-equal/commit/ed266e8571d1efdbb4ece0a01f849cbc57461076)
- [Refactor] there is no need to do further compareation when two types are not equal [`6a5efc1`](https://github.com/inspect-js/node-deep-equal/commit/6a5efc130e96398de55cf517969746c52735f34b)
- Only apps should have lockfiles [`f60442f`](https://github.com/inspect-js/node-deep-equal/commit/f60442fa6fd10004366d80481ada7df668bd6adb)
- [Tests] clean up redundant test [`cbbef21`](https://github.com/inspect-js/node-deep-equal/commit/cbbef21045e3b2e1ee30f6d0a83381d489df296e)
- [Fix] use `is-date-object` to properly detect cross-realm Dates [`0c5b21a`](https://github.com/inspect-js/node-deep-equal/commit/0c5b21ad245d1427caefe20b2bace752cbe5e493)
- [meta] Add copyright statements [`56a7746`](https://github.com/inspect-js/node-deep-equal/commit/56a774674f55bb3e0627aaec4dda90e4a06df7d6)
- [Dev Deps] update `tape` [`3e9c1b9`](https://github.com/inspect-js/node-deep-equal/commit/3e9c1b903703e32bbed45fddcae815eb25822fd4)

## [v1.0.1](https://github.com/inspect-js/node-deep-equal/compare/v1.0.0...v1.0.1) - 2015-08-29

### Fixed

- Null should == undefined [`#23`](https://github.com/inspect-js/node-deep-equal/issues/23)

### Commits

- Update travis.yml to modern node [`5055802`](https://github.com/inspect-js/node-deep-equal/commit/5055802274a85a3e1493fa1b7378499554c6a4dc)

## [v1.0.0](https://github.com/inspect-js/node-deep-equal/compare/v0.2.2...v1.0.0) - 2015-02-07

## [v0.2.2](https://github.com/inspect-js/node-deep-equal/compare/v0.2.1...v0.2.2) - 2015-02-07

### Commits

- latest tape [`1d18617`](https://github.com/inspect-js/node-deep-equal/commit/1d18617608316a034c8ee6727838c9b82614f0f9)
- add type check to support browsers [`3acb0d7`](https://github.com/inspect-js/node-deep-equal/commit/3acb0d7fb8915fb7f1ca97701b5761793738bad3)

## [v0.2.1](https://github.com/inspect-js/node-deep-equal/compare/v0.2.0...v0.2.1) - 2014-01-29

### Commits

- license file [`b3431d4`](https://github.com/inspect-js/node-deep-equal/commit/b3431d48e0f7c406a08d0e077f2dd9580d4561ae)

## [v0.2.0](https://github.com/inspect-js/node-deep-equal/compare/v0.1.2...v0.2.0) - 2014-01-29

### Commits

- buffer check without Buffer [`5271f39`](https://github.com/inspect-js/node-deep-equal/commit/5271f39f3ce61fecb71fa6ca28cb4a8a8b963bbd)
- failing test for buffer comparison [`cc8990b`](https://github.com/inspect-js/node-deep-equal/commit/cc8990b9d84a2267d84253e21c9f7db238e961e9)

## [v0.1.2](https://github.com/inspect-js/node-deep-equal/compare/v0.1.1...v0.1.2) - 2013-12-20

### Commits

- test the arguments shim and properly factor out the arguments handling [`cf88b9c`](https://github.com/inspect-js/node-deep-equal/commit/cf88b9c2e87d6050ba1a46a82be216d0244ee428)
- more coverage for the keys shim [`7a41f0e`](https://github.com/inspect-js/node-deep-equal/commit/7a41f0ec813616b0e1272c2bf463028a84d51c99)
- test coverage for the non-object case [`abd3358`](https://github.com/inspect-js/node-deep-equal/commit/abd33586af0006e8492bbcdb1eb27a306359b03c)
- fix the argument comparisons to properly call equal() [`0114a10`](https://github.com/inspect-js/node-deep-equal/commit/0114a10398e386ab0e073966e8525cd2ce125de4)
- more code coverage for date comparisons [`41ac40e`](https://github.com/inspect-js/node-deep-equal/commit/41ac40e844b10833a75a39ea92e73bcdb1ae3cd9)
- use the latest version of tape [`f8341cb`](https://github.com/inspect-js/node-deep-equal/commit/f8341cbff475ee7ee1ec651662cb5bd621c75838)
- use https: for the badges [`514b332`](https://github.com/inspect-js/node-deep-equal/commit/514b332e9c2390c89c5beac8cf866c2f9a4afa9e)
- 100% coverage [`e55aac7`](https://github.com/inspect-js/node-deep-equal/commit/e55aac78a6e013d6f5a10febc7009476a8f10ca5)

## [v0.1.1](https://github.com/inspect-js/node-deep-equal/compare/v0.1.0...v0.1.1) - 2013-12-20

### Commits

- Fixed arguments support for IE8- [`174d41a`](https://github.com/inspect-js/node-deep-equal/commit/174d41a5559820458648d237ebedfe78e44f7430)

## [v0.1.0](https://github.com/inspect-js/node-deep-equal/compare/v0.0.0...v0.1.0) - 2013-10-14

### Commits

- upgrade the markdown [`30e1f8f`](https://github.com/inspect-js/node-deep-equal/commit/30e1f8fe5b1fb7d5d68b156bf69bb04a7bb807a3)
- opts.strict instead of a boolean arg [`58cbdb2`](https://github.com/inspect-js/node-deep-equal/commit/58cbdb2a97e623a68310833218af5ada6c97c2ce)
- using testling-ci [`064af17`](https://github.com/inspect-js/node-deep-equal/commit/064af170f895fc67ec4bbf654e7de27721cf40c0)
- Add optional strict parameter [`f3413b1`](https://github.com/inspect-js/node-deep-equal/commit/f3413b1d95e261f735939fbcaee65e598fe92852)
- strict equality test [`281189b`](https://github.com/inspect-js/node-deep-equal/commit/281189b6962c9c57105474d01a8127da3318f555)
- document opts.strict [`69fae5a`](https://github.com/inspect-js/node-deep-equal/commit/69fae5ab4b9278e28d09864c62dcfe9b4f00c1d8)
- using tape [`4384db8`](https://github.com/inspect-js/node-deep-equal/commit/4384db8a9cc7af1cf79e1976d1b36e66934b2b4c)
- using travis [`e508692`](https://github.com/inspect-js/node-deep-equal/commit/e5086928e1edf120d227d5e7f5e6ac4d755248a1)
- deepEquals comparing nulls seems to break in ie6-8 [`b2cfeb9`](https://github.com/inspect-js/node-deep-equal/commit/b2cfeb95da6982f8bc2e676231820a3a47385db3)
- performance note in the readme [`da9bfc9`](https://github.com/inspect-js/node-deep-equal/commit/da9bfc9aadfb9045bbf8157e9403de7184b404fd)
- more firefoxes [`3d60ec5`](https://github.com/inspect-js/node-deep-equal/commit/3d60ec58d6fe269c13c5be04b749f01972aa5b1c)

## v0.0.0 - 2012-02-10

### Commits

- implementation with an example [`2dd56f9`](https://github.com/inspect-js/node-deep-equal/commit/2dd56f9e6d42e841c74289c2e2d489047d077622)
- readme [`4a44f99`](https://github.com/inspect-js/node-deep-equal/commit/4a44f99bea3277e32d9842d9d49ac21f316ac74d)
- and a test [`357ccd3`](https://github.com/inspect-js/node-deep-equal/commit/357ccd37d23ba7814c8fe284d36d6e86a39b7526)
- actually just MIT [`3d348c8`](https://github.com/inspect-js/node-deep-equal/commit/3d348c88fc92ce1d69a3fedbb2aa8cb6ea1480f7)
