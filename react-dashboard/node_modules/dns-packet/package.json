{"name": "dns-packet", "version": "5.6.1", "description": "An abstract-encoding compliant module for encoding / decoding DNS packets", "author": "<PERSON>", "license": "MIT", "repository": "mafintosh/dns-packet", "homepage": "https://github.com/mafintosh/dns-packet", "engines": {"node": ">=6"}, "scripts": {"clean": "rm -rf coverage .nyc_output/", "lint": "eslint --color *.js examples/*.js", "pretest": "npm run lint", "test": "tape test.js", "coverage": "nyc -r html npm test"}, "dependencies": {"@leichtgewicht/ip-codec": "^2.0.1"}, "devDependencies": {"eslint": "^5.14.1", "eslint-config-standard": "^12.0.0", "eslint-plugin-import": "^2.16.0", "eslint-plugin-node": "^8.0.1", "eslint-plugin-promise": "^4.0.1", "eslint-plugin-standard": "^4.0.0", "tape": "^4.10.1"}, "keywords": ["dns", "packet", "encodings", "encoding", "encoder", "abstract-encoding"], "files": ["index.js", "types.js", "rcodes.js", "opcodes.js", "classes.js", "optioncodes.js"]}